/**
 * @fileoverview Progress bar component that shows which slide
 * is the currently displayed slide.
 */

import React, { Component } from 'react'

interface Slide {
  // Define other properties of a slide object if they exist and are used
  duration?: number; // Optional duration in seconds for this specific slide
  // Example: id: string; content: string;
}

interface ProgressProps {
  current: number;
  defaultDuration: number; // Assuming this is in milliseconds if slide.duration is in seconds
  orderedSlides: Slide[];
  ready: boolean;
}

interface ProgressState {} // No state used

class Progress extends Component<ProgressProps, ProgressState> {
  render() {
    const { current, defaultDuration, orderedSlides, ready } = this.props
    return (
      <div className='flex flex-row w-full absolute bottom-0 left-0 h-1 bg-black bg-opacity-30'>
        {orderedSlides.map((slide, i) => (
          <div
            key={`slide-${i}`}
            className={`flex-1 h-full relative overflow-hidden ${i < current ? 'bg-white' : 'bg-transparent'}`}
          >
            <div
              className={`h-full bg-white transition-all ease-linear ${i === current && ready ? 'w-full' : 'w-0'}`}
              style={{
                transitionDuration: i === current && ready
                  ? `${slide.duration || defaultDuration / 1000}s`
                  : '0s',
              }}
            />
            {/* Separator line between segments */}
            {i < orderedSlides.length - 1 && (
              <div className='absolute top-0 right-0 w-px h-full bg-black bg-opacity-50' />
            )}
          </div>
        ))}
      </div>
    )
  }
}

export default Progress