import React, { Component } from 'react'
import { Form, Input, InlineInputGroup } from '../../../components/Form'
import getVideoId from 'get-video-id'
import * as z from 'zod'

import { IYoutubeDefaultData } from '../index' // This is an interface
import YoutubeContent, { YoutubeWidgetDataSchema } from './YoutubeContent' // Import Zod schema

/*
 * Zod schema for YoutubeOptions props
 * IWidgetOptionsEditorProps<T> has data: T | undefined, onChange: (newData: T) => void
 */
export const YoutubeOptionsPropsSchema = z.object({
  data: YoutubeWidgetDataSchema.optional(),
  onChange: z.function().args(YoutubeWidgetDataSchema).returns(z.void()),
})
export type IYoutubeOptionsProps = z.infer<typeof YoutubeOptionsPropsSchema>;

// Zod schema for the component's local state
export const YoutubeOptionsComponentStateSchema = z.object({
  inputUrl: z.string(),
})
type IYoutubeOptionsComponentState = z.infer<typeof YoutubeOptionsComponentStateSchema>;

class YoutubeOptions extends Component<IYoutubeOptionsProps, IYoutubeOptionsComponentState> {
  constructor(props: IYoutubeOptionsProps) {
    super(props)
    /*
     * Initialize local state for the URL input field.
     * If props.data.video_id exists, construct a sample URL for display,
     * otherwise, use an empty string or a default example URL.
     */
    this.state = {
      inputUrl: props.data?.video_id ? `https://www.youtube.com/watch?v=${props.data.video_id}` : '',
    }
  }
  
  componentDidUpdate(prevProps: IYoutubeOptionsProps) {
    /*
     * If the video_id from parent data changes and it's different from what inputUrl would yield,
     * update inputUrl. This is tricky because inputUrl could be a full URL or just an ID.
     * A safer approach might be to primarily work with video_id and only use inputUrl for user input.
     */
    if (this.props.data?.video_id !== prevProps.data?.video_id) {
        this.setState({
            inputUrl: this.props.data?.video_id ? `https://www.youtube.com/watch?v=${this.props.data.video_id}` : ''
        })
    }
  }

  // Handles changes for any field in IYoutubeDefaultData or the local inputUrl
  handleChange = (name: string, value: any): void => {
    const { onChange, data: currentWidgetData } = this.props

    if (name === 'inputUrl') {
      this.setState({ inputUrl: value as string })
      // Try to extract video_id and update parent
      const extracted = getVideoId(value as string)
      if (extracted && extracted.service === 'youtube' && extracted.id) {
        const newData: IYoutubeDefaultData = {
          ...(currentWidgetData || this.getDefaultWidgetData()), // Spread existing or default data
          video_id: extracted.id, // Update video_id
        }
        if (onChange) onChange(newData)
      } else if (!value) { // If URL is cleared, clear video_id
        const newData: IYoutubeDefaultData = {
            ...(currentWidgetData || this.getDefaultWidgetData()),
            video_id: null,
        }
        if (onChange) onChange(newData)
      }
      /*
       * If invalid URL, video_id in parent remains unchanged or becomes null if input is empty.
       * User will see their inputUrl, but the underlying video_id might not update.
       */
      return
    }
    
    // For other fields, directly update the widget data object
    if (onChange) {
      const newData: IYoutubeDefaultData = {
        ...(currentWidgetData || this.getDefaultWidgetData()), // Spread existing or default data
        [name]: value, // Update the changed field
      }
      onChange(newData)
    }
  }

  // Helper to get default widget data structure
  getDefaultWidgetData = (): IYoutubeDefaultData => {
    return {
      video_id: null,
      autoplay: true,
      loop: false,
      show_controls: true,
      start_time: 0,
      end_time: 0,
      show_captions: false,
    }
  }

  render() {
    // Current widget data comes from props, managed by WidgetEditDialog
    const { data: currentWidgetData } = this.props
    const { inputUrl } = this.state

    // Use values from props.data for controlled components, with fallbacks to defaults
    const {
      video_id = null, // Primarily use this for the preview and for constructing inputUrl if empty
      autoplay = true,
      loop = false,
      show_controls = true,
      start_time = 0,
      end_time = 0,
      show_captions = false,
    } = currentWidgetData || this.getDefaultWidgetData()
    
    const previewData: IYoutubeDefaultData = { video_id, autoplay, loop, show_controls, start_time, end_time, show_captions }

    return (
      <div className={'options-container'}>
        <Form>
          <h3>Widget: YouTube Video</h3>
          <p>Configure the YouTube video widget.</p>
          <Input
            label={'YouTube Video URL or ID'}
            type={'text'}
            name={'inputUrl'} // Use local state 'inputUrl' for this field
            value={inputUrl} // Controlled by local state
            placeholder={'e.g., https://www.youtube.com/watch?v=... or just video_id'}
            onChange={this.handleChange}
            helpText={`Current Video ID: ${video_id || 'None'}`}
          />
          <InlineInputGroup>
            <Input
              label={'Start Time (seconds)'}
              type={'number'}
              name={'start_time'}
              value={start_time}
              min={0}
              onChange={this.handleChange}
            />
            <Input
              label={'End Time (seconds, 0 for end)'}
              type={'number'}
              name={'end_time'}
              value={end_time}
              min={0}
              onChange={this.handleChange}
            />
          </InlineInputGroup>
          <InlineInputGroup>
            <Input
              type='checkbox'
              name='autoplay'
              label='Autoplay'
              checked={autoplay}
              onChange={this.handleChange}
            />
            <Input
              type='checkbox'
              name='loop'
              label='Loop'
              checked={loop}
              onChange={this.handleChange}
            />
            <Input
              type='checkbox'
              name='show_controls'
              label='Show Controls'
              checked={show_controls}
              onChange={this.handleChange}
            />
            <Input
              type='checkbox'
              name='show_captions'
              label='Show Captions'
              checked={show_captions}
              onChange={this.handleChange}
            />
          </InlineInputGroup>
        </Form>
        <div className={'preview-section-container'}>
            <p>Preview</p>
            <div className={'preview-box youtube-preview-box'}>
                {video_id ? (
                    <YoutubeContent data={previewData} isPreview={true} />
                ) : (
                    <div className='flex items-center justify-center h-full text-center'>Enter a valid YouTube URL or Video ID</div>
                )}
            </div>
        </div>
        
      </div>
    )
  }
}

export default YoutubeOptions