{"name": "DigitalSignage", "version": "1.0.0", "description": "This is a user interface for a dynamic digital signage", "main": "index.js", "scripts": {"setup": "[ -f .env ] || makeconf", "dev": "next dev ", "build": "next build", "build:fast": "DISABLE_ESLINT_PLUGIN=true next build --no-lint", "start": "next start", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore .", "lint:fix": "eslint --cache --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore . --fix", "type-check": "tsc --noEmit", "test": "jest --coverage", "update": "git pull && bun install && bun run build", "heroku-postbuild": "next build", "prepare": "husky", "seed:admin": "bun run create-admin.ts", "seed:meeting-rooms": "bun run scripts/seedMeetingAdmin.ts", "setup:meeting-rooms": "node scripts/setup-meeting-rooms-simple.js", "init-feature-flags": "node scripts/init-feature-flags.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/msal-node": "^3.6.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.79.0", "@types/gridstack": "^0.5.0", "@types/passport-google-oauth20": "^2.0.16", "animated-scroll-to": "^1.2.2", "axios": "^1.9.0", "boxen": "^4.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^0.3.1", "dotenv": "^7.0.0", "get-video-id": "^3.1.0", "glob": "^7.1.3", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "gridstack": "^12.2.1", "isomorphic-unfetch": "^3.0.0", "js-cookie": "^2.2.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "makeconf": "^1.4.4", "mongoose": "^8.15.1", "mongoose-crud-generator": "^1.0.5", "multer": "^2.0.0", "next": "^15.3.3", "next-cookies": "^2.0.3", "next-themes": "^0.4.6", "nookies": "^2.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^6.3.0", "passport-oauth2": "^1.8.0", "react": "^18.2.0", "react-color": "^2.17.0", "react-content-loader": "^7.0.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-live-clock": "^6.1.25", "react-lottie": "^1.2.3", "react-markdown": "^9.0.1", "react-modal": "^3.8.1", "react-switch": "^7.0.0", "react-youtube": "^7.9.0", "shortid": "^2.2.14", "sonner": "^2.0.5", "superagent": "^5.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "turbo": "^2.3.0", "tw-animate-css": "^1.3.4", "uuid": "^8.3.2", "webfontloader": "^1.6.28", "zod": "^3.25.56"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/axios": "^0.14.4", "@types/boxen": "^3.0.5", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "22.15.30", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/passport-local-mongoose": "^6.1.5", "@types/react": "19.1.6", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.5", "@types/react-dropzone": "^4.2.2", "@types/react-lottie": "^1.2.10", "@types/react-modal": "^3.16.3", "@types/react-youtube": "^7.6.2", "@types/shortid": "^2.2.0", "@types/superagent": "^8.1.9", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/webfontloader": "^1.6.38", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.29", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8.5.4", "supertest": "^7.1.1", "tailwindcss": "^4.1.8", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}