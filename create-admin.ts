import mongoose from "mongoose";
import User from "./lib/models/User";

const MONGODB_URI =
  "mongodb+srv://dimastw:<EMAIL>/digital-signage?retryWrites=true&w=majority&appName=Cluster0";

async function createAdmin() {
  try {
    console.log("🔗 Connecting to MongoDB...");
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    } as mongoose.ConnectOptions);
    console.log("✅ Connected to MongoDB successfully");

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: "<EMAIL>" });

    if (existingAdmin) {
      console.log("👤 Admin user already exists!");
      console.log("Email: <EMAIL>");
      console.log("You can login with this user");
      process.exit(0);
    }

    // Create admin user
    console.log("👤 Creating admin user...");
    const adminUser = new User({
      email: "<EMAIL>",
      name: "Administrator",
      role: "admin",
    });

    const registeredUser = await new Promise<any>((resolve, reject) => {
      User.register(adminUser, "admin123", (err: any, user: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(user);
        }
      });
    });

    console.log("✅ Admin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: admin123");
    console.log("");
    console.log(
      "🎯 You can now login to the admin panel with these credentials"
    );
    console.log("");
    console.log("💡 For meeting room management setup, run:");
    console.log("   npx ts-node scripts/seedMeetingAdmin.ts");

    await mongoose.disconnect();
    process.exit(0);
  } catch (error) {
    console.error("❌ Error:", error);
    process.exit(1);
  }
}

createAdmin();
