"use client";

import * as React from "react";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import { Button } from "@/components/ui/button";

export function ThemeToggle() {
  const { setTheme, theme, resolvedTheme, systemTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  // useEffect only runs on the client, so now we can safely show the UI
  React.useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = React.useCallback(() => {
    const currentTheme = resolvedTheme || theme || systemTheme || "light";
    const newTheme = currentTheme === "light" ? "dark" : "light";
    console.log("Theme toggle clicked:", {
      theme,
      resolvedTheme,
      systemTheme,
      current: currentTheme,
      new: newTheme
    });
    setTheme(newTheme);
  }, [setTheme, theme, resolvedTheme, systemTheme]);

  if (!mounted) {
    return (
      <Button
        variant='ghost'
        size='icon'
        className='h-8 w-8'
        disabled
      >
        <Sun className='h-4 w-4' />
        <span className='sr-only'>Toggle theme</span>
      </Button>
    );
  }

  const currentTheme = resolvedTheme || theme || "light";
  const isDark = currentTheme === "dark";

  return (
    <Button
      variant='ghost'
      size='icon'
      onClick={handleToggle}
      className='h-8 w-8 relative'
      title={`Switch to ${isDark ? "light" : "dark"} mode`}
      type='button'
    >
      <Sun
        className={`h-4 w-4 transition-all duration-300 ${
          isDark ? "rotate-90 scale-0" : "rotate-0 scale-100"
        }`}
      />
      <Moon
        className={`absolute h-4 w-4 transition-all duration-300 ${
          isDark ? "rotate-0 scale-100" : "-rotate-90 scale-0"
        }`}
      />
      <span className='sr-only'>Toggle theme</span>
    </Button>
  );
}
