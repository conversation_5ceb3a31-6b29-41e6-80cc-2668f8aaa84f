# Logs
logs
*.log
npm-debug.log*
.env
.DS_Store

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules
jspm_packages

# Optional npm cache directory
.npm

# Bower
bower_components/

# Optional REPL history
.node_repl_history
# IDE/Editor data
.idea

# Next
.next

package-lock.json
yarn.lock
node_modules
uploads/*

dist

*.tsbuildinfo
type-check-errors.log
bun.lockb
.eslintcache
.eslintrc.old
tsconfig.tsbuildinfo
docs
repomix-output.xml
public/uploads/