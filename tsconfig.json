{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "jsxFactory": "React.createElement", "jsxFragmentFactory": "React.Fragment", "incremental": true, "allowImportingTsExtensions": true, "baseUrl": ".", "paths": {"@/*": ["*"], "~/*": ["./*"]}, "types": ["jest"], "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "types/**/*.d.ts", ".next/types/**/*.ts", "jest.setup.js"], "exclude": ["node_modules", "dist", "__tests__", "scripts"]}