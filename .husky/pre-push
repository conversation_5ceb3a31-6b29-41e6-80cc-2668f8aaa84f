#!/usr/bin/env sh

echo "Running pre-push hook: TypeScript checks and build..."

# Run TypeScript checks
echo "Running TypeScript checks (~/.bun/bin/bun run type-check)..."
~/.bun/bin/bun run type-check
if [ $? -ne 0 ]; then
  echo "TypeScript checks failed. Push aborted."
  exit 1
fi
echo "TypeScript checks passed."

# Run build
echo "Running build (~/.bun/bin/bun run build)..."
~/.bun/bin/bun run build
if [ $? -ne 0 ]; then
  echo "Build failed. Push aborted."
  exit 1
fi
echo "Build passed."

echo "Pre-push checks passed. Proceeding with push."
exit 0
