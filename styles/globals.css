@import "gridstack/dist/gridstack.min.css";

@import "tailwindcss";
@import "tw-animate-css";
@import "./themes.css";

@custom-variant dark (&:is(.dark *));

@custom-variant fixed (&:is(.layout-fixed *));

@theme inline {
  --breakpoint-3xl: 1600px;
  --breakpoint-4xl: 2000px;
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-surface: var(--surface);
  --color-surface-foreground: var(--surface-foreground);
  --color-code: var(--code);
  --color-code-foreground: var(--code-foreground);
  --color-code-highlight: var(--code-highlight);
  --color-code-number: var(--code-number);
  --color-selection: var(--selection);
  --color-selection-foreground: var(--selection-foreground);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: var(--color-blue-300);
  --chart-2: var(--color-blue-500);
  --chart-3: var(--color-blue-600);
  --chart-4: var(--color-blue-700);
  --chart-5: var(--color-blue-800);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --surface: oklch(0.98 0 0);
  --surface-foreground: var(--foreground);
  --code: var(--surface);
  --code-foreground: var(--surface-foreground);
  --code-highlight: oklch(0.96 0 0);
  --code-number: oklch(0.56 0 0);
  --selection: oklch(0.145 0 0);
  --selection-foreground: oklch(1 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: var(--color-blue-300);
  --chart-2: var(--color-blue-500);
  --chart-3: var(--color-blue-600);
  --chart-4: var(--color-blue-700);
  --chart-5: var(--color-blue-800);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);
  --surface: oklch(0.2 0 0);
  --surface-foreground: oklch(0.708 0 0);
  --code: var(--surface);
  --code-foreground: var(--surface-foreground);
  --code-highlight: oklch(0.27 0 0);
  --code-number: oklch(0.72 0 0);
  --selection: oklch(0.922 0 0);
  --selection-foreground: oklch(0.205 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  ::selection {
    @apply bg-selection text-selection-foreground;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
  }

  @supports (font: -apple-system-body) and (-webkit-appearance: none) {
    [data-wrapper] {
      @apply min-[1800px]:border-t;
    }
  }

  a:active,
  button:active {
    @apply opacity-60 md:opacity-100;
  }
}

@utility border-grid {
  @apply border-border/50 dark:border-border;
}

@utility section-soft {
  @apply from-background to-surface/40 dark:bg-background 3xl:fixed:bg-none bg-gradient-to-b;
}

@utility theme-container {
  @apply font-sans;
}

@utility container-wrapper {

  /* GridStack Styling */
  .grid-stack {
    background: transparent;
    height: 100% !important;
    width: 100% !important;
    position: relative;
    overflow: hidden;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Full screen display styling */
  .display-grid {
    height: 100vh !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .grid-stack-item {
    border: 2px solid transparent;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: transparent;
  }

  /* Ensure grid items and their content fill the space */
  .grid-stack-item-content {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* For display mode, remove borders and ensure full coverage */
  .display-grid .grid-stack-item {
    border: none !important;
    border-radius: 0 !important;
  }

  .grid-stack-item:hover {
    border-color: rgba(59, 130, 246, 0.5);
  }

  .grid-stack-item.ui-draggable-dragging {
    border-color: rgba(239, 68, 68, 0.8) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    z-index: 1000 !important;
  }

  .grid-stack-item.ui-resizable-resizing {
    border-color: rgba(16, 185, 129, 0.8) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .grid-stack-item-content {
    background: transparent;
    border: none;
    border-radius: 6px;
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  /* GridStack drag handle styling */
  .gridstack-drag-handle {
    cursor: move;
    user-select: none;
  }

  .gridstack-no-drag {
    cursor: default;
    pointer-events: auto;
  }

  /* GridStack resize handles */
  .grid-stack-item>.ui-resizable-handle {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .grid-stack-item:hover>.ui-resizable-handle {
    opacity: 1;
  }

  .grid-stack-item>.ui-resizable-se {
    background: rgba(59, 130, 246, 0.8);
    border-radius: 0 0 6px 0;
    width: 12px;
    height: 12px;
    right: 0;
    bottom: 0;
  }

  .grid-stack-item>.ui-resizable-sw {
    background: rgba(59, 130, 246, 0.8);
    border-radius: 0 0 0 6px;
    width: 12px;
    height: 12px;
    left: 0;
    bottom: 0;
  }

  .grid-stack-item>.ui-resizable-ne {
    background: rgba(59, 130, 246, 0.8);
    border-radius: 0 6px 0 0;
    width: 12px;
    height: 12px;
    right: 0;
    top: 0;
  }

  .grid-stack-item>.ui-resizable-nw {
    background: rgba(59, 130, 246, 0.8);
    border-radius: 6px 0 0 0;
    width: 12px;
    height: 12px;
    left: 0;
    top: 0;
  }

  .grid-stack-item>.ui-resizable-n {
    background: rgba(59, 130, 246, 0.8);
    height: 6px;
    top: 0;
    left: 12px;
    right: 12px;
  }

  .grid-stack-item>.ui-resizable-s {
    background: rgba(59, 130, 246, 0.8);
    height: 6px;
    bottom: 0;
    left: 12px;
    right: 12px;
  }

  .grid-stack-item>.ui-resizable-e {
    background: rgba(59, 130, 246, 0.8);
    width: 6px;
    right: 0;
    top: 12px;
    bottom: 12px;
  }

  .grid-stack-item>.ui-resizable-w {
    background: rgba(59, 130, 246, 0.8);
    width: 6px;
    left: 0;
    top: 12px;
    bottom: 12px;
  }

}

@utility container {
  @apply 3xl:max-w-screen-2xl mx-auto max-w-[1400px] px-4 lg:px-8;
}

@utility no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

@utility border-ghost {
  @apply after:border-border relative after:absolute after:inset-0 after:border after:mix-blend-darken dark:after:mix-blend-lighten;
}

@utility step {
  counter-increment: step;
  @apply relative;

  &:before {
    @apply text-muted-foreground right-0 mr-2 hidden size-7 items-center justify-center rounded-full text-center -indent-px font-mono text-sm font-medium md:absolute;
    content: counter(step);
  }
}

@utility extend-touch-target {
  @media (pointer: coarse) {
    @apply relative touch-manipulation after:absolute after:-inset-2;
  }
}

@layer components {
  .steps {
    &:first-child {
      @apply !mt-0;
    }

    &:first-child>h3:first-child {
      @apply !mt-0;
    }

    >h3 {
      @apply !mt-16;
    }

    >h3+p {
      @apply !mt-2;
    }
  }

  [data-rehype-pretty-code-figure] {
    background-color: var(--color-code);
    color: var(--color-code-foreground);
    border-radius: var(--radius-lg);
    border-width: 0px;
    border-color: var(--border);
    margin-top: calc(var(--spacing) * 6);
    overflow: hidden;
    font-size: var(--text-sm);
    outline: none;
    position: relative;
    @apply md:-mx-4;

    &:has([data-rehype-pretty-code-title]) [data-slot="copy-button"] {
      top: calc(var(--spacing) * 1.5) !important;
    }
  }

  [data-rehype-pretty-code-title] {
    border-bottom: color-mix(in oklab, var(--border) 30%, transparent);
    border-bottom-width: 1px;
    border-bottom-style: solid;
    padding-block: calc(var(--spacing) * 2.5);
    padding-inline: calc(var(--spacing) * 4);
    font-size: var(--text-sm);
    font-family: var(--font-mono);
    color: var(--color-code-foreground);
  }

  [data-line-numbers] {
    display: grid;
    min-width: 100%;
    white-space: pre;
    border: 0;
    background: transparent;
    padding: 0;
    counter-reset: line;
    box-decoration-break: clone;
  }

  [data-line-numbers] [data-line]::before {
    font-size: var(--text-sm);
    counter-increment: line;
    content: counter(line);
    display: inline-block;
    width: calc(var(--spacing) * 16);
    padding-right: calc(var(--spacing) * 6);
    text-align: right;
    color: var(--color-code-number);
    background-color: var(--color-code);
    position: sticky;
    left: 0;
  }

  [data-line-numbers] [data-highlighted-line][data-line]::before {
    background-color: var(--color-code-highlight);
  }

  [data-line] {
    padding-top: calc(var(--spacing) * 0.5);
    padding-bottom: calc(var(--spacing) * 0.5);
    min-height: calc(var(--spacing) * 1);
    width: 100%;
    display: inline-block;
  }

  [data-line] span {
    color: var(--shiki-light);

    @variant dark {
      color: var(--shiki-dark) !important;
    }
  }

  [data-highlighted-line],
  [data-highlighted-chars] {
    position: relative;
    background-color: var(--color-code-highlight);
  }

  [data-highlighted-line] {
    &:after {
      position: absolute;
      top: 0;
      left: 0;
      width: 2px;
      height: 100%;
      content: "";
      background-color: color-mix(in oklab,
          var(--muted-foreground) 50%,
          transparent);
    }
  }

  [data-highlighted-chars] {
    border-radius: var(--radius-sm);
    padding-inline: 0.3rem;
    padding-block: 0.1rem;
    font-family: var(--font-mono);
    font-size: 0.8rem;
  }
}

/* Enhanced Frame Component Animations */
@keyframes fade-in-50 {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slide-in-from-bottom-4 {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes gradient-shift {

  0%,
  100% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in-50 {
  animation: fade-in-50 0.5s ease-out;
}

.slide-in-from-bottom-4 {
  animation: slide-in-from-bottom-4 0.7s ease-out;
}

.duration-700 {
  animation-duration: 0.7s;
}

/* Glass morphism effects */
.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Custom card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Enhanced status indicator pulse */
.status-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

/* Gradient animations for modern look */
.gradient-animation {
  background-size: 200% 200%;
  animation: gradient-shift 6s ease infinite;
}

/* Smooth sidebar transitions */
.sidebar-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced border animations */
.border-glow {
  position: relative;
}

.border-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(45deg, transparent, rgba(var(--primary), 0.3), transparent);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.border-glow:hover::before {
  opacity: 1;
}

/* Custom scrollbar for sidebar */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(var(--muted-foreground), 0.3);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--muted-foreground), 0.5);
}

/* Widget Edit Dialog Specific Styles */
.widget-settings-modal {
  /* Ensure proper viewport constraints */
  max-height: calc(100vh - 2rem);
  max-width: min(90vw, 1024px);
}

.widget-settings-modal .widget-settings-body {
  /* Smooth scrolling for widget options */
  scroll-behavior: smooth;
}

/* Custom scrollbar for widget dialog content */
.widget-settings-modal .overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.widget-settings-modal .overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.widget-settings-modal .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.widget-settings-modal .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Dark mode scrollbar */
.dark .widget-settings-modal .overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark .widget-settings-modal .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .widget-settings-modal .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Ensure proper spacing for complex widget forms */
.widget-settings-modal .widget-preview-section,
.widget-settings-modal .widget-section {
  margin-bottom: 1.5rem;
}

.widget-settings-modal .widget-preview-section:last-child,
.widget-settings-modal .widget-section:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments for smaller screens */
@media (max-height: 600px) {
  .widget-settings-modal {
    max-height: calc(100vh - 1rem);
  }
}

@media (max-width: 640px) {
  .widget-settings-modal {
    max-width: calc(100vw - 1rem);
    margin: 0.5rem;
  }
}